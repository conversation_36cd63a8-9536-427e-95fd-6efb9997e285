#!/usr/bin/env python3
"""
Test script for MongoDB context usage in agent responses.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "atlas-q-a-rag"))

from app.agents.langgraph_agent import LangGraphAgent
from app.models.bot_config import AgentConfig


class MockLLMResponse:
    def __init__(self, content):
        self.content = content


class MockLLM:
    def invoke(self, messages):
        # Extract context from messages
        if isinstance(messages, list):
            user_content = ""
            for msg in messages:
                if isinstance(msg, dict) and msg.get("role") == "user":
                    user_content = msg.get("content", "")
                    break
        else:
            user_content = str(messages)

        # Check if MongoDB data is in the context
        if "user tester" in user_content and "Software Engineer" in user_content:
            return MockLLMResponse(
                "user tester hakkında bilgiler şunlardır: <PERSON>si<PERSON>: user tester, <PERSON><PERSON><PERSON>on: Software Engineer, "
                "Şirket: Atlas University, Departman: IT Department, E-posta: <EMAIL>, "
                "Telefon: +90 ************, Çalışan ID: EMP001. Bu bilgiler veritabanından alınmıştır."
            )
        else:
            return MockLLMResponse("Üzgünüm, user tester hakkında bilgi bulamadım.")


async def test_mongodb_context():
    """Test the agent's ability to use MongoDB context in responses."""

    # Create a simple agent config
    agent_config = AgentConfig(
        model="gpt-4o-mini",
        type="langgraph",
        config={"temperature": 0.1, "max_tokens": 1000},
    )

    # Simple prompts
    system_prompt = """You are a helpful assistant. When you receive database information, use it to provide specific answers. Always incorporate relevant database information into your responses naturally."""

    query_prompt = "Answer the user's question using the provided context."

    # Initialize the agent
    agent = LangGraphAgent(agent_config, system_prompt, query_prompt)

    # Mock MongoDB tool results (similar to what we saw in the logs)
    mock_tool_results = {
        "MongoDBQueryTool": {
            "success": True,
            "collection": "MasterData_Product",
            "results": [
                {
                    "_id": None,
                    "SourceType": "API",
                    "SourceName": "SempleApi",
                    "EntityType": "Product",
                    "ExternalId": "19007",
                    "CreatedAt": "2025-07-11T13:43:40.791000",
                    "UpdatedAt": "2025-07-11T13:43:40.791000",
                    "id": 19007,
                    "nameSurname": "user tester",
                    "photoName": "default.jpg",
                    "userGuid": "test-guid-123",
                    "jobTitle": "Software Engineer",
                    "company": "Atlas University",
                    "department": "IT Department",
                    "title": "Senior Developer",
                    "samAccountName": "utester",
                    "description": "Test user for system validation",
                    "employeeId": "EMP001",
                    "eMail": "<EMAIL>",
                    "dateOfBirth": "1990-01-01",
                    "phoneNumber": "+90 ************",
                    "description2": "Additional description",
                    "cardId": "CARD123",
                    "blockDescription": "No blocks",
                    "additional": "Additional info",
                }
            ],
            "count": 1,
            "query": {},
            "llm_used": False,
        }
    }

    # Test query
    test_query = "user tester hakkında bilgi ver"

    print("🧪 Testing MongoDB context usage in agent response...")
    print(f"Query: {test_query}")
    print("\n" + "=" * 50)

    try:
        # Process the query
        result = await agent.process_query(test_query, mock_tool_results)

        if result.get("error"):
            print(f"❌ Error: {result['error']}")
            return False

        response = result.get("response", "")
        print(f"Response: {response}")
        print("\n" + "=" * 50)

        # Check if the response uses the MongoDB data
        mongodb_data_used = any(
            [
                "user tester" in response.lower(),
                "software engineer" in response.lower(),
                "atlas university" in response.lower(),
                "it department" in response.lower(),
                "<EMAIL>" in response.lower(),
                "+90 ************" in response,
                "emp001" in response.lower(),
            ]
        )

        if mongodb_data_used:
            print("✅ SUCCESS: Response includes specific MongoDB data!")
            return True
        else:
            print("❌ FAILURE: Response does not use MongoDB data")
            print("Expected to see specific information like:")
            print("- user tester")
            print("- Software Engineer")
            print("- Atlas University")
            print("- <EMAIL>")
            return False

    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        return False


if __name__ == "__main__":
    print("🧪 Testing MongoDB context usage in agent responses...")
    success = asyncio.run(test_mongodb_context())

    if success:
        print("\n✅ Test passed! Agent is using MongoDB context properly.")
    else:
        print("\n❌ Test failed. Agent is not using MongoDB context effectively.")

    sys.exit(0 if success else 1)
